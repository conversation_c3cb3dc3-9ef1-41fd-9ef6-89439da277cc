#!/usr/bin/env swift

import Foundation

/// 测试自动同步功能的脚本
/// 创建于 2025-08-18T14:18:07+08:00
class AutoSyncTester {
    
    func testFullSync() async {
        print("🚀 开始测试全量同步功能...")
        
        // 检查 Claude 目录是否存在
        let claudeDirectory = FileManager.default.homeDirectoryForCurrentUser.appendingPathComponent(".claude")
        print("📁 检查 Claude 目录: \(claudeDirectory.path)")
        
        guard FileManager.default.fileExists(atPath: claudeDirectory.path) else {
            print("❌ Claude 目录不存在，请先运行 Claude CLI")
            return
        }
        
        // 扫描 JSONL 文件
        do {
            let jsonlFiles = try scanJSONLFiles(in: claudeDirectory)
            print("📄 发现 \(jsonlFiles.count) 个 JSONL 文件")
            
            if jsonlFiles.isEmpty {
                print("⚠️ 未发现任何 JSONL 文件，创建测试数据...")
                try createTestData(in: claudeDirectory)
            } else {
                for file in jsonlFiles.prefix(3) {
                    print("   - \(file.lastPathComponent)")
                }
                if jsonlFiles.count > 3 {
                    print("   ... 共 \(jsonlFiles.count) 个文件")
                }
            }
        } catch {
            print("❌ 扫描文件失败: \(error)")
        }
    }
    
    func scanJSONLFiles(in directory: URL) throws -> [URL] {
        let fileManager = FileManager.default
        let resourceKeys = Set<URLResourceKey>([.nameKey, .isDirectoryKey])
        
        guard let enumerator = fileManager.enumerator(
            at: directory,
            includingPropertiesForKeys: Array(resourceKeys),
            options: [.skipsHiddenFiles]
        ) else {
            throw NSError(domain: "AutoSyncTester", code: 1, userInfo: [NSLocalizedDescriptionKey: "无法创建文件枚举器"])
        }
        
        var jsonlFiles: [URL] = []
        
        for case let fileURL as URL in enumerator {
            let resourceValues = try fileURL.resourceValues(forKeys: resourceKeys)
            
            if let isDirectory = resourceValues.isDirectory, !isDirectory,
               let name = resourceValues.name, name.hasSuffix(".jsonl") {
                jsonlFiles.append(fileURL)
            }
        }
        
        return jsonlFiles.sorted { $0.lastPathComponent < $1.lastPathComponent }
    }
    
    func createTestData(in directory: URL) throws {
        let testFile = directory.appendingPathComponent("test_usage_\(Int(Date().timeIntervalSince1970)).jsonl")
        
        let testEntries = [
            """
            {"timestamp":"2025-08-18T14:18:07+08:00","model":"claude-3-5-sonnet-20241022","usage":{"input_tokens":150,"output_tokens":300},"session_id":"test_session_1","conversation_id":"test_conv_1","request_id":"test_req_1"}
            """,
            """
            {"timestamp":"2025-08-18T14:17:07+08:00","model":"claude-3-5-sonnet-20241022","usage":{"input_tokens":200,"output_tokens":400},"session_id":"test_session_2","conversation_id":"test_conv_2","request_id":"test_req_2"}
            """,
            """
            {"timestamp":"2025-08-18T14:16:07+08:00","model":"claude-3-haiku-20240307","usage":{"input_tokens":100,"output_tokens":150},"session_id":"test_session_3","conversation_id":"test_conv_3","request_id":"test_req_3"}
            """
        ]
        
        let content = testEntries.joined(separator: "\n")
        try content.write(to: testFile, atomically: true, encoding: .utf8)
        
        print("✅ 创建测试数据文件: \(testFile.lastPathComponent)")
    }
    
    func testTimerSync() {
        print("⏰ 开始测试定时同步功能...")
        print("   请在应用中启用自动同步功能")
        print("   设置较短的同步间隔（如5分钟）")
        print("   观察应用中的同步状态指示器")
        
        // 监听同步相关的通知
        let notificationCenter = NotificationCenter.default
        
        // 这些通知名称需要与应用中定义的保持一致
        let notifications = [
            "ClaudeBar.syncServiceDidStart",
            "ClaudeBar.usageDataSyncDidStart", 
            "ClaudeBar.syncStatusDidChange",
            "ClaudeBar.usageDataSyncDidComplete"
        ]
        
        for notificationName in notifications {
            notificationCenter.addObserver(
                forName: NSNotification.Name(notificationName),
                object: nil,
                queue: .main
            ) { notification in
                print("📢 收到通知: \(notification.name.rawValue)")
                if let userInfo = notification.userInfo {
                    for (key, value) in userInfo {
                        print("   \(key): \(value)")
                    }
                }
            }
        }
        
        print("✅ 已设置通知监听器")
    }
    
    func run() async {
        print("🔄 ClaudeBar 自动同步功能测试")
        print("===================================")
        
        await testFullSync()
        print("")
        testTimerSync()
        
        print("")
        print("📋 测试建议:")
        print("1. 在应用设置中启用自动同步")
        print("2. 设置同步间隔为 5 分钟")
        print("3. 观察主界面中的同步状态")
        print("4. 点击手动同步按钮测试立即同步")
        print("5. 查看控制台日志输出")
    }
}

// 运行测试
Task {
    let tester = AutoSyncTester()
    await tester.run()
    
    // 保持脚本运行以监听通知
    print("⏳ 监听同步通知中... (按 Ctrl+C 退出)")
    RunLoop.main.run()
}