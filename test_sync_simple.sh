#!/bin/bash

# 自动同步功能测试脚本
# 创建于 2025-08-18T14:24:30+08:00

echo "🔄 ClaudeBar 自动同步功能测试"
echo "================================="

# 1. 检查数据文件
echo "📊 数据文件统计:"
JSONL_COUNT=$(find ~/.claude -name "*.jsonl" | wc -l | tr -d ' ')
echo "   JSONL 文件数量: $JSONL_COUNT"

# 检查最新的几个文件
echo "   最新修改的文件:"
find ~/.claude -name "*.jsonl" -exec stat -f "%m %N" {} \; | sort -nr | head -3 | while read timestamp file; do
    date_str=$(date -r $timestamp "+%Y-%m-%d %H:%M:%S")
    basename=$(basename "$file")
    echo "   - $basename ($date_str)"
done

# 2. 检查数据库状态
echo ""
echo "🗄️ 数据库状态:"
DB_PATH="$HOME/Library/Application Support/ClaudeBar/usage_statistics.db"
if [ -f "$DB_PATH" ]; then
    echo "   数据库文件存在: $(basename "$DB_PATH")"
    DB_SIZE=$(stat -f "%z" "$DB_PATH")
    echo "   数据库大小: $DB_SIZE bytes"
    
    # 如果有 sqlite3，检查表状态
    if command -v sqlite3 >/dev/null; then
        RECORD_COUNT=$(sqlite3 "$DB_PATH" "SELECT COUNT(*) FROM usage_entries;" 2>/dev/null || echo "N/A")
        echo "   记录数量: $RECORD_COUNT"
    fi
else
    echo "   数据库文件不存在，将在首次同步时创建"
fi

# 3. 检查应用状态
echo ""
echo "📱 应用状态:"
if pgrep -f "ClaudeBar" >/dev/null; then
    echo "   ✅ ClaudeBar 应用正在运行"
    
    # 检查控制台日志中的同步相关信息
    echo "   📋 最近的同步日志:"
    log show --predicate 'subsystem == "com.claude.configmanager"' --style compact --last 5m 2>/dev/null | grep -i sync | tail -5 || echo "   (未发现同步日志)"
else
    echo "   ❌ ClaudeBar 应用未运行"
    echo "   请先启动应用以测试自动同步功能"
fi

# 4. 提供测试建议
echo ""
echo "🧪 测试步骤建议:"
echo "1. 确保 ClaudeBar 应用正在运行"
echo "2. 打开应用主窗口 (点击菜单栏图标)"
echo "3. 进入设置页面"
echo "4. 启用自动同步功能"
echo "5. 设置同步间隔为 5 分钟（便于测试）"
echo "6. 进入使用统计页面"
echo "7. 点击手动同步按钮测试立即同步"
echo "8. 观察同步状态指示器的变化"

echo ""
echo "📈 预期结果:"
echo "- 同步状态从'空闲'变为'扫描文件'->'解析数据'->'同步中'->'同步完成'"
echo "- 数据库中应有 $JSONL_COUNT 个文件的使用数据"
echo "- 使用统计界面显示最新的统计数据"
echo "- 定时器应在设定间隔后自动触发下次同步"

echo ""
echo "🔍 监控命令:"
echo "# 实时查看同步日志:"
echo "log stream --predicate 'subsystem == \"com.claude.configmanager\"' | grep -i sync"
echo ""
echo "# 检查数据库记录数:"
echo "sqlite3 \"$DB_PATH\" \"SELECT COUNT(*) FROM usage_entries;\""